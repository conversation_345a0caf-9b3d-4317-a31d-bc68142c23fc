{"ast": null, "code": "var MIN_SIZE = 20;\nexport function getSpinSize() {\n  var containerSize = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 0;\n  var scrollRange = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;\n  var baseSize = containerSize / scrollRange * containerSize;\n  if (isNaN(baseSize)) {\n    baseSize = 0;\n  }\n  baseSize = Math.max(baseSize, MIN_SIZE);\n  return Math.floor(baseSize);\n}", "map": {"version": 3, "names": ["MIN_SIZE", "getSpinSize", "containerSize", "arguments", "length", "undefined", "scrollRange", "baseSize", "isNaN", "Math", "max", "floor"], "sources": ["E:/安装包/Capstone-CS77-2025-S2-main (2)/Capstone-CS77-2025-S2-main/frontend/node_modules/rc-virtual-list/es/utils/scrollbarUtil.js"], "sourcesContent": ["var MIN_SIZE = 20;\nexport function getSpinSize() {\n  var containerSize = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 0;\n  var scrollRange = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;\n  var baseSize = containerSize / scrollRange * containerSize;\n  if (isNaN(baseSize)) {\n    baseSize = 0;\n  }\n  baseSize = Math.max(baseSize, MIN_SIZE);\n  return Math.floor(baseSize);\n}"], "mappings": "AAAA,IAAIA,QAAQ,GAAG,EAAE;AACjB,OAAO,SAASC,WAAWA,CAAA,EAAG;EAC5B,IAAIC,aAAa,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC;EACzF,IAAIG,WAAW,GAAGH,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC;EACvF,IAAII,QAAQ,GAAGL,aAAa,GAAGI,WAAW,GAAGJ,aAAa;EAC1D,IAAIM,KAAK,CAACD,QAAQ,CAAC,EAAE;IACnBA,QAAQ,GAAG,CAAC;EACd;EACAA,QAAQ,GAAGE,IAAI,CAACC,GAAG,CAACH,QAAQ,EAAEP,QAAQ,CAAC;EACvC,OAAOS,IAAI,CAACE,KAAK,CAACJ,QAAQ,CAAC;AAC7B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}