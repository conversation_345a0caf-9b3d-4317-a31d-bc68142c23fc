{"ast": null, "code": "import rules from \"../rule\";\nimport { isEmptyValue } from \"../util\";\nvar date = function date(rule, value, callback, source, options) {\n  // console.log('integer rule called %j', rule);\n  var errors = [];\n  var validate = rule.required || !rule.required && source.hasOwnProperty(rule.field);\n  // console.log('validate on %s value', value);\n  if (validate) {\n    if (isEmptyValue(value, 'date') && !rule.required) {\n      return callback();\n    }\n    rules.required(rule, value, source, errors, options);\n    if (!isEmptyValue(value, 'date')) {\n      var dateObject;\n      if (value instanceof Date) {\n        dateObject = value;\n      } else {\n        dateObject = new Date(value);\n      }\n      rules.type(rule, dateObject, source, errors, options);\n      if (dateObject) {\n        rules.range(rule, dateObject.getTime(), source, errors, options);\n      }\n    }\n  }\n  callback(errors);\n};\nexport default date;", "map": {"version": 3, "names": ["rules", "isEmptyValue", "date", "rule", "value", "callback", "source", "options", "errors", "validate", "required", "hasOwnProperty", "field", "dateObject", "Date", "type", "range", "getTime"], "sources": ["E:/安装包/Capstone-CS77-2025-S2-main (2)/Capstone-CS77-2025-S2-main/frontend/node_modules/@rc-component/async-validator/es/validator/date.js"], "sourcesContent": ["import rules from \"../rule\";\nimport { isEmptyValue } from \"../util\";\nvar date = function date(rule, value, callback, source, options) {\n  // console.log('integer rule called %j', rule);\n  var errors = [];\n  var validate = rule.required || !rule.required && source.hasOwnProperty(rule.field);\n  // console.log('validate on %s value', value);\n  if (validate) {\n    if (isEmptyValue(value, 'date') && !rule.required) {\n      return callback();\n    }\n    rules.required(rule, value, source, errors, options);\n    if (!isEmptyValue(value, 'date')) {\n      var dateObject;\n      if (value instanceof Date) {\n        dateObject = value;\n      } else {\n        dateObject = new Date(value);\n      }\n      rules.type(rule, dateObject, source, errors, options);\n      if (dateObject) {\n        rules.range(rule, dateObject.getTime(), source, errors, options);\n      }\n    }\n  }\n  callback(errors);\n};\nexport default date;"], "mappings": "AAAA,OAAOA,KAAK,MAAM,SAAS;AAC3B,SAASC,YAAY,QAAQ,SAAS;AACtC,IAAIC,IAAI,GAAG,SAASA,IAAIA,CAACC,IAAI,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,OAAO,EAAE;EAC/D;EACA,IAAIC,MAAM,GAAG,EAAE;EACf,IAAIC,QAAQ,GAAGN,IAAI,CAACO,QAAQ,IAAI,CAACP,IAAI,CAACO,QAAQ,IAAIJ,MAAM,CAACK,cAAc,CAACR,IAAI,CAACS,KAAK,CAAC;EACnF;EACA,IAAIH,QAAQ,EAAE;IACZ,IAAIR,YAAY,CAACG,KAAK,EAAE,MAAM,CAAC,IAAI,CAACD,IAAI,CAACO,QAAQ,EAAE;MACjD,OAAOL,QAAQ,CAAC,CAAC;IACnB;IACAL,KAAK,CAACU,QAAQ,CAACP,IAAI,EAAEC,KAAK,EAAEE,MAAM,EAAEE,MAAM,EAAED,OAAO,CAAC;IACpD,IAAI,CAACN,YAAY,CAACG,KAAK,EAAE,MAAM,CAAC,EAAE;MAChC,IAAIS,UAAU;MACd,IAAIT,KAAK,YAAYU,IAAI,EAAE;QACzBD,UAAU,GAAGT,KAAK;MACpB,CAAC,MAAM;QACLS,UAAU,GAAG,IAAIC,IAAI,CAACV,KAAK,CAAC;MAC9B;MACAJ,KAAK,CAACe,IAAI,CAACZ,IAAI,EAAEU,UAAU,EAAEP,MAAM,EAAEE,MAAM,EAAED,OAAO,CAAC;MACrD,IAAIM,UAAU,EAAE;QACdb,KAAK,CAACgB,KAAK,CAACb,IAAI,EAAEU,UAAU,CAACI,OAAO,CAAC,CAAC,EAAEX,MAAM,EAAEE,MAAM,EAAED,OAAO,CAAC;MAClE;IACF;EACF;EACAF,QAAQ,CAACG,MAAM,CAAC;AAClB,CAAC;AACD,eAAeN,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}