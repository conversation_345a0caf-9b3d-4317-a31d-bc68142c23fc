{"ast": null, "code": "import raf from \"rc-util/es/raf\";\nimport * as React from 'react';\nfunction smoothScrollOffset(offset) {\n  return Math.floor(Math.pow(offset, 0.5));\n}\nexport function getPageXY(e, horizontal) {\n  var obj = 'touches' in e ? e.touches[0] : e;\n  return obj[horizontal ? 'pageX' : 'pageY'] - window[horizontal ? 'scrollX' : 'scrollY'];\n}\nexport default function useScrollDrag(inVirtual, componentRef, onScrollOffset) {\n  React.useEffect(function () {\n    var ele = componentRef.current;\n    if (inVirtual && ele) {\n      var mouseDownLock = false;\n      var rafId;\n      var _offset;\n      var stopScroll = function stopScroll() {\n        raf.cancel(rafId);\n      };\n      var continueScroll = function continueScroll() {\n        stopScroll();\n        rafId = raf(function () {\n          onScrollOffset(_offset);\n          continueScroll();\n        });\n      };\n      var onMouseDown = function onMouseDown(e) {\n        // Skip if element set draggable\n        if (e.target.draggable || e.button !== 0) {\n          return;\n        }\n        // Skip if nest List has handled this event\n        var event = e;\n        if (!event._virtualHandled) {\n          event._virtualHandled = true;\n          mouseDownLock = true;\n        }\n      };\n      var onMouseUp = function onMouseUp() {\n        mouseDownLock = false;\n        stopScroll();\n      };\n      var onMouseMove = function onMouseMove(e) {\n        if (mouseDownLock) {\n          var mouseY = getPageXY(e, false);\n          var _ele$getBoundingClien = ele.getBoundingClientRect(),\n            top = _ele$getBoundingClien.top,\n            bottom = _ele$getBoundingClien.bottom;\n          if (mouseY <= top) {\n            var diff = top - mouseY;\n            _offset = -smoothScrollOffset(diff);\n            continueScroll();\n          } else if (mouseY >= bottom) {\n            var _diff = mouseY - bottom;\n            _offset = smoothScrollOffset(_diff);\n            continueScroll();\n          } else {\n            stopScroll();\n          }\n        }\n      };\n      ele.addEventListener('mousedown', onMouseDown);\n      ele.ownerDocument.addEventListener('mouseup', onMouseUp);\n      ele.ownerDocument.addEventListener('mousemove', onMouseMove);\n      return function () {\n        ele.removeEventListener('mousedown', onMouseDown);\n        ele.ownerDocument.removeEventListener('mouseup', onMouseUp);\n        ele.ownerDocument.removeEventListener('mousemove', onMouseMove);\n        stopScroll();\n      };\n    }\n  }, [inVirtual]);\n}", "map": {"version": 3, "names": ["raf", "React", "smoothScrollOffset", "offset", "Math", "floor", "pow", "getPageXY", "e", "horizontal", "obj", "touches", "window", "useScrollDrag", "inVirtual", "componentRef", "onScrollOffset", "useEffect", "ele", "current", "mouseDownLock", "rafId", "_offset", "stopScroll", "cancel", "continueScroll", "onMouseDown", "target", "draggable", "button", "event", "_virtualHandled", "onMouseUp", "onMouseMove", "mouseY", "_ele$getBoundingClien", "getBoundingClientRect", "top", "bottom", "diff", "_diff", "addEventListener", "ownerDocument", "removeEventListener"], "sources": ["E:/安装包/Capstone-CS77-2025-S2-main (2)/Capstone-CS77-2025-S2-main/frontend/node_modules/rc-virtual-list/es/hooks/useScrollDrag.js"], "sourcesContent": ["import raf from \"rc-util/es/raf\";\nimport * as React from 'react';\nfunction smoothScrollOffset(offset) {\n  return Math.floor(Math.pow(offset, 0.5));\n}\nexport function getPageXY(e, horizontal) {\n  var obj = 'touches' in e ? e.touches[0] : e;\n  return obj[horizontal ? 'pageX' : 'pageY'] - window[horizontal ? 'scrollX' : 'scrollY'];\n}\nexport default function useScrollDrag(inVirtual, componentRef, onScrollOffset) {\n  React.useEffect(function () {\n    var ele = componentRef.current;\n    if (inVirtual && ele) {\n      var mouseDownLock = false;\n      var rafId;\n      var _offset;\n      var stopScroll = function stopScroll() {\n        raf.cancel(rafId);\n      };\n      var continueScroll = function continueScroll() {\n        stopScroll();\n        rafId = raf(function () {\n          onScrollOffset(_offset);\n          continueScroll();\n        });\n      };\n      var onMouseDown = function onMouseDown(e) {\n        // Skip if element set draggable\n        if (e.target.draggable || e.button !== 0) {\n          return;\n        }\n        // Skip if nest List has handled this event\n        var event = e;\n        if (!event._virtualHandled) {\n          event._virtualHandled = true;\n          mouseDownLock = true;\n        }\n      };\n      var onMouseUp = function onMouseUp() {\n        mouseDownLock = false;\n        stopScroll();\n      };\n      var onMouseMove = function onMouseMove(e) {\n        if (mouseDownLock) {\n          var mouseY = getPageXY(e, false);\n          var _ele$getBoundingClien = ele.getBoundingClientRect(),\n            top = _ele$getBoundingClien.top,\n            bottom = _ele$getBoundingClien.bottom;\n          if (mouseY <= top) {\n            var diff = top - mouseY;\n            _offset = -smoothScrollOffset(diff);\n            continueScroll();\n          } else if (mouseY >= bottom) {\n            var _diff = mouseY - bottom;\n            _offset = smoothScrollOffset(_diff);\n            continueScroll();\n          } else {\n            stopScroll();\n          }\n        }\n      };\n      ele.addEventListener('mousedown', onMouseDown);\n      ele.ownerDocument.addEventListener('mouseup', onMouseUp);\n      ele.ownerDocument.addEventListener('mousemove', onMouseMove);\n      return function () {\n        ele.removeEventListener('mousedown', onMouseDown);\n        ele.ownerDocument.removeEventListener('mouseup', onMouseUp);\n        ele.ownerDocument.removeEventListener('mousemove', onMouseMove);\n        stopScroll();\n      };\n    }\n  }, [inVirtual]);\n}"], "mappings": "AAAA,OAAOA,GAAG,MAAM,gBAAgB;AAChC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,kBAAkBA,CAACC,MAAM,EAAE;EAClC,OAAOC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,GAAG,CAACH,MAAM,EAAE,GAAG,CAAC,CAAC;AAC1C;AACA,OAAO,SAASI,SAASA,CAACC,CAAC,EAAEC,UAAU,EAAE;EACvC,IAAIC,GAAG,GAAG,SAAS,IAAIF,CAAC,GAAGA,CAAC,CAACG,OAAO,CAAC,CAAC,CAAC,GAAGH,CAAC;EAC3C,OAAOE,GAAG,CAACD,UAAU,GAAG,OAAO,GAAG,OAAO,CAAC,GAAGG,MAAM,CAACH,UAAU,GAAG,SAAS,GAAG,SAAS,CAAC;AACzF;AACA,eAAe,SAASI,aAAaA,CAACC,SAAS,EAAEC,YAAY,EAAEC,cAAc,EAAE;EAC7Ef,KAAK,CAACgB,SAAS,CAAC,YAAY;IAC1B,IAAIC,GAAG,GAAGH,YAAY,CAACI,OAAO;IAC9B,IAAIL,SAAS,IAAII,GAAG,EAAE;MACpB,IAAIE,aAAa,GAAG,KAAK;MACzB,IAAIC,KAAK;MACT,IAAIC,OAAO;MACX,IAAIC,UAAU,GAAG,SAASA,UAAUA,CAAA,EAAG;QACrCvB,GAAG,CAACwB,MAAM,CAACH,KAAK,CAAC;MACnB,CAAC;MACD,IAAII,cAAc,GAAG,SAASA,cAAcA,CAAA,EAAG;QAC7CF,UAAU,CAAC,CAAC;QACZF,KAAK,GAAGrB,GAAG,CAAC,YAAY;UACtBgB,cAAc,CAACM,OAAO,CAAC;UACvBG,cAAc,CAAC,CAAC;QAClB,CAAC,CAAC;MACJ,CAAC;MACD,IAAIC,WAAW,GAAG,SAASA,WAAWA,CAAClB,CAAC,EAAE;QACxC;QACA,IAAIA,CAAC,CAACmB,MAAM,CAACC,SAAS,IAAIpB,CAAC,CAACqB,MAAM,KAAK,CAAC,EAAE;UACxC;QACF;QACA;QACA,IAAIC,KAAK,GAAGtB,CAAC;QACb,IAAI,CAACsB,KAAK,CAACC,eAAe,EAAE;UAC1BD,KAAK,CAACC,eAAe,GAAG,IAAI;UAC5BX,aAAa,GAAG,IAAI;QACtB;MACF,CAAC;MACD,IAAIY,SAAS,GAAG,SAASA,SAASA,CAAA,EAAG;QACnCZ,aAAa,GAAG,KAAK;QACrBG,UAAU,CAAC,CAAC;MACd,CAAC;MACD,IAAIU,WAAW,GAAG,SAASA,WAAWA,CAACzB,CAAC,EAAE;QACxC,IAAIY,aAAa,EAAE;UACjB,IAAIc,MAAM,GAAG3B,SAAS,CAACC,CAAC,EAAE,KAAK,CAAC;UAChC,IAAI2B,qBAAqB,GAAGjB,GAAG,CAACkB,qBAAqB,CAAC,CAAC;YACrDC,GAAG,GAAGF,qBAAqB,CAACE,GAAG;YAC/BC,MAAM,GAAGH,qBAAqB,CAACG,MAAM;UACvC,IAAIJ,MAAM,IAAIG,GAAG,EAAE;YACjB,IAAIE,IAAI,GAAGF,GAAG,GAAGH,MAAM;YACvBZ,OAAO,GAAG,CAACpB,kBAAkB,CAACqC,IAAI,CAAC;YACnCd,cAAc,CAAC,CAAC;UAClB,CAAC,MAAM,IAAIS,MAAM,IAAII,MAAM,EAAE;YAC3B,IAAIE,KAAK,GAAGN,MAAM,GAAGI,MAAM;YAC3BhB,OAAO,GAAGpB,kBAAkB,CAACsC,KAAK,CAAC;YACnCf,cAAc,CAAC,CAAC;UAClB,CAAC,MAAM;YACLF,UAAU,CAAC,CAAC;UACd;QACF;MACF,CAAC;MACDL,GAAG,CAACuB,gBAAgB,CAAC,WAAW,EAAEf,WAAW,CAAC;MAC9CR,GAAG,CAACwB,aAAa,CAACD,gBAAgB,CAAC,SAAS,EAAET,SAAS,CAAC;MACxDd,GAAG,CAACwB,aAAa,CAACD,gBAAgB,CAAC,WAAW,EAAER,WAAW,CAAC;MAC5D,OAAO,YAAY;QACjBf,GAAG,CAACyB,mBAAmB,CAAC,WAAW,EAAEjB,WAAW,CAAC;QACjDR,GAAG,CAACwB,aAAa,CAACC,mBAAmB,CAAC,SAAS,EAAEX,SAAS,CAAC;QAC3Dd,GAAG,CAACwB,aAAa,CAACC,mBAAmB,CAAC,WAAW,EAAEV,WAAW,CAAC;QAC/DV,UAAU,CAAC,CAAC;MACd,CAAC;IACH;EACF,CAAC,EAAE,CAACT,SAAS,CAAC,CAAC;AACjB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}