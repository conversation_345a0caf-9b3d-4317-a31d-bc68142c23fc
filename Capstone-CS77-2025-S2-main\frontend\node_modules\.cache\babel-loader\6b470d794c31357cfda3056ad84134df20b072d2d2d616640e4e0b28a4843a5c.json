{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport useEvent from \"rc-util/es/hooks/useEvent\";\nimport * as React from 'react';\nimport { unstable_batchedUpdates } from 'react-dom';\nimport channelUpdate from \"./channelUpdate\";\n/**\n * Batcher for record any `useEffectState` need update.\n */\nexport function useBatcher() {\n  // Updater Trigger\n  var updateFuncRef = React.useRef(null);\n\n  // Notify update\n  var notifyEffectUpdate = function notifyEffectUpdate(callback) {\n    if (!updateFuncRef.current) {\n      updateFuncRef.current = [];\n      channelUpdate(function () {\n        unstable_batchedUpdates(function () {\n          updateFuncRef.current.forEach(function (fn) {\n            fn();\n          });\n          updateFuncRef.current = null;\n        });\n      });\n    }\n    updateFuncRef.current.push(callback);\n  };\n  return notifyEffectUpdate;\n}\n\n/**\n * Trigger state update by `useLayoutEffect` to save perf.\n */\nexport default function useEffectState(notifyEffectUpdate, defaultValue) {\n  // Value\n  var _React$useState = React.useState(defaultValue),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    stateValue = _React$useState2[0],\n    setStateValue = _React$useState2[1];\n\n  // Set State\n  var setEffectVal = useEvent(function (nextValue) {\n    notifyEffectUpdate(function () {\n      setStateValue(nextValue);\n    });\n  });\n  return [stateValue, setEffectVal];\n}", "map": {"version": 3, "names": ["_slicedToArray", "useEvent", "React", "unstable_batchedUpdates", "channelUpdate", "useBatcher", "updateFuncRef", "useRef", "notifyEffectUpdate", "callback", "current", "for<PERSON>ach", "fn", "push", "useEffectState", "defaultValue", "_React$useState", "useState", "_React$useState2", "stateValue", "setStateValue", "setEffectVal", "nextValue"], "sources": ["E:/安装包/Capstone-CS77-2025-S2-main (2)/Capstone-CS77-2025-S2-main/frontend/node_modules/rc-overflow/es/hooks/useEffectState.js"], "sourcesContent": ["import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport useEvent from \"rc-util/es/hooks/useEvent\";\nimport * as React from 'react';\nimport { unstable_batchedUpdates } from 'react-dom';\nimport channelUpdate from \"./channelUpdate\";\n/**\n * Batcher for record any `useEffectState` need update.\n */\nexport function useBatcher() {\n  // Updater Trigger\n  var updateFuncRef = React.useRef(null);\n\n  // Notify update\n  var notifyEffectUpdate = function notifyEffectUpdate(callback) {\n    if (!updateFuncRef.current) {\n      updateFuncRef.current = [];\n      channelUpdate(function () {\n        unstable_batchedUpdates(function () {\n          updateFuncRef.current.forEach(function (fn) {\n            fn();\n          });\n          updateFuncRef.current = null;\n        });\n      });\n    }\n    updateFuncRef.current.push(callback);\n  };\n  return notifyEffectUpdate;\n}\n\n/**\n * Trigger state update by `useLayoutEffect` to save perf.\n */\nexport default function useEffectState(notifyEffectUpdate, defaultValue) {\n  // Value\n  var _React$useState = React.useState(defaultValue),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    stateValue = _React$useState2[0],\n    setStateValue = _React$useState2[1];\n\n  // Set State\n  var setEffectVal = useEvent(function (nextValue) {\n    notifyEffectUpdate(function () {\n      setStateValue(nextValue);\n    });\n  });\n  return [stateValue, setEffectVal];\n}"], "mappings": "AAAA,OAAOA,cAAc,MAAM,0CAA0C;AACrE,OAAOC,QAAQ,MAAM,2BAA2B;AAChD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,uBAAuB,QAAQ,WAAW;AACnD,OAAOC,aAAa,MAAM,iBAAiB;AAC3C;AACA;AACA;AACA,OAAO,SAASC,UAAUA,CAAA,EAAG;EAC3B;EACA,IAAIC,aAAa,GAAGJ,KAAK,CAACK,MAAM,CAAC,IAAI,CAAC;;EAEtC;EACA,IAAIC,kBAAkB,GAAG,SAASA,kBAAkBA,CAACC,QAAQ,EAAE;IAC7D,IAAI,CAACH,aAAa,CAACI,OAAO,EAAE;MAC1BJ,aAAa,CAACI,OAAO,GAAG,EAAE;MAC1BN,aAAa,CAAC,YAAY;QACxBD,uBAAuB,CAAC,YAAY;UAClCG,aAAa,CAACI,OAAO,CAACC,OAAO,CAAC,UAAUC,EAAE,EAAE;YAC1CA,EAAE,CAAC,CAAC;UACN,CAAC,CAAC;UACFN,aAAa,CAACI,OAAO,GAAG,IAAI;QAC9B,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ;IACAJ,aAAa,CAACI,OAAO,CAACG,IAAI,CAACJ,QAAQ,CAAC;EACtC,CAAC;EACD,OAAOD,kBAAkB;AAC3B;;AAEA;AACA;AACA;AACA,eAAe,SAASM,cAAcA,CAACN,kBAAkB,EAAEO,YAAY,EAAE;EACvE;EACA,IAAIC,eAAe,GAAGd,KAAK,CAACe,QAAQ,CAACF,YAAY,CAAC;IAChDG,gBAAgB,GAAGlB,cAAc,CAACgB,eAAe,EAAE,CAAC,CAAC;IACrDG,UAAU,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAChCE,aAAa,GAAGF,gBAAgB,CAAC,CAAC,CAAC;;EAErC;EACA,IAAIG,YAAY,GAAGpB,QAAQ,CAAC,UAAUqB,SAAS,EAAE;IAC/Cd,kBAAkB,CAAC,YAAY;MAC7BY,aAAa,CAACE,SAAS,CAAC;IAC1B,CAAC,CAAC;EACJ,CAAC,CAAC;EACF,OAAO,CAACH,UAAU,EAAEE,YAAY,CAAC;AACnC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}