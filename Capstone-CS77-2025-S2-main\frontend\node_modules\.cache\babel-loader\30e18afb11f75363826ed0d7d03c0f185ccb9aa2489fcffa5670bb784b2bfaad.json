{"ast": null, "code": "import * as React from 'react';\nimport ResizeObserver from 'rc-resize-observer';\nimport useLayoutEffect from \"rc-util/es/hooks/useLayoutEffect\";\nexport default function MeasureCell(_ref) {\n  var columnKey = _ref.columnKey,\n    onColumnResize = _ref.onColumnResize,\n    column = _ref.column;\n  var cellRef = React.useRef();\n  useLayoutEffect(function () {\n    if (cellRef.current) {\n      onColumnResize(columnKey, cellRef.current.offsetWidth);\n    }\n  }, []);\n  return /*#__PURE__*/React.createElement(ResizeObserver, {\n    data: columnKey\n  }, /*#__PURE__*/React.createElement(\"td\", {\n    ref: cellRef,\n    style: {\n      paddingTop: 0,\n      paddingBottom: 0,\n      borderTop: 0,\n      borderBottom: 0,\n      height: 0\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      height: 0,\n      overflow: 'hidden',\n      fontWeight: 'bold'\n    }\n  }, (column === null || column === void 0 ? void 0 : column.title) || '\\xa0')));\n}", "map": {"version": 3, "names": ["React", "ResizeObserver", "useLayoutEffect", "MeasureCell", "_ref", "column<PERSON>ey", "onColumnResize", "column", "cellRef", "useRef", "current", "offsetWidth", "createElement", "data", "ref", "style", "paddingTop", "paddingBottom", "borderTop", "borderBottom", "height", "overflow", "fontWeight", "title"], "sources": ["E:/安装包/Capstone-CS77-2025-S2-main (2)/Capstone-CS77-2025-S2-main/frontend/node_modules/rc-table/es/Body/MeasureCell.js"], "sourcesContent": ["import * as React from 'react';\nimport ResizeObserver from 'rc-resize-observer';\nimport useLayoutEffect from \"rc-util/es/hooks/useLayoutEffect\";\nexport default function MeasureCell(_ref) {\n  var columnKey = _ref.columnKey,\n    onColumnResize = _ref.onColumnResize,\n    column = _ref.column;\n  var cellRef = React.useRef();\n  useLayoutEffect(function () {\n    if (cellRef.current) {\n      onColumnResize(columnKey, cellRef.current.offsetWidth);\n    }\n  }, []);\n  return /*#__PURE__*/React.createElement(ResizeObserver, {\n    data: columnKey\n  }, /*#__PURE__*/React.createElement(\"td\", {\n    ref: cellRef,\n    style: {\n      paddingTop: 0,\n      paddingBottom: 0,\n      borderTop: 0,\n      borderBottom: 0,\n      height: 0\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      height: 0,\n      overflow: 'hidden',\n      fontWeight: 'bold'\n    }\n  }, (column === null || column === void 0 ? void 0 : column.title) || '\\xa0')));\n}"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,cAAc,MAAM,oBAAoB;AAC/C,OAAOC,eAAe,MAAM,kCAAkC;AAC9D,eAAe,SAASC,WAAWA,CAACC,IAAI,EAAE;EACxC,IAAIC,SAAS,GAAGD,IAAI,CAACC,SAAS;IAC5BC,cAAc,GAAGF,IAAI,CAACE,cAAc;IACpCC,MAAM,GAAGH,IAAI,CAACG,MAAM;EACtB,IAAIC,OAAO,GAAGR,KAAK,CAACS,MAAM,CAAC,CAAC;EAC5BP,eAAe,CAAC,YAAY;IAC1B,IAAIM,OAAO,CAACE,OAAO,EAAE;MACnBJ,cAAc,CAACD,SAAS,EAAEG,OAAO,CAACE,OAAO,CAACC,WAAW,CAAC;IACxD;EACF,CAAC,EAAE,EAAE,CAAC;EACN,OAAO,aAAaX,KAAK,CAACY,aAAa,CAACX,cAAc,EAAE;IACtDY,IAAI,EAAER;EACR,CAAC,EAAE,aAAaL,KAAK,CAACY,aAAa,CAAC,IAAI,EAAE;IACxCE,GAAG,EAAEN,OAAO;IACZO,KAAK,EAAE;MACLC,UAAU,EAAE,CAAC;MACbC,aAAa,EAAE,CAAC;MAChBC,SAAS,EAAE,CAAC;MACZC,YAAY,EAAE,CAAC;MACfC,MAAM,EAAE;IACV;EACF,CAAC,EAAE,aAAapB,KAAK,CAACY,aAAa,CAAC,KAAK,EAAE;IACzCG,KAAK,EAAE;MACLK,MAAM,EAAE,CAAC;MACTC,QAAQ,EAAE,QAAQ;MAClBC,UAAU,EAAE;IACd;EACF,CAAC,EAAE,CAACf,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACgB,KAAK,KAAK,MAAM,CAAC,CAAC,CAAC;AAChF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}